"use client"

import React from "react"
import { Newsletter } from "@prisma/client"
import { Mail, Calendar, ToggleLeft, ToggleRight, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DataTable from "@/components/dashboard/components/table/DataTable"
import { DataTableOnActionDef } from "@/components/dashboard/components/table/types"

interface NewsletterTableProps {
  data: Newsletter[]
  onDelete: DataTableOnActionDef<Newsletter>
  onToggleStatus: (id: string, isActive: boolean) => Promise<{ success: boolean; message: string }>
}

export default function NewsletterTable({ data, onDelete, onToggleStatus }: NewsletterTableProps) {
  const handleToggleStatus = async (subscriber: Newsletter) => {
    try {
      const result = await onToggleStatus(subscriber.id, !subscriber.isActive)
      if (result.success) {
        // يمكن إضافة toast notification هنا
        console.log(result.message)
      }
    } catch (error) {
      console.error("Error toggling status:", error)
    }
  }

  return (
    <DataTable<Newsletter>
      data={data}
      onDelete={onDelete}
      columns={[
        {
          accessorKey: "email",
          header: "البريد الإلكتروني",
          cell: ({ row }) => (
            <div className="flex items-center gap-2">
              <Mail className="size-4 text-gray-500" />
              <span className="font-medium" dir="ltr">{row.original.email}</span>
            </div>
          ),
        },
        {
          accessorKey: "isActive",
          header: "الحالة",
          cell: ({ row }) => (
            <div className="flex items-center gap-2">
              {row.original.isActive ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  نشط
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  غير نشط
                </span>
              )}
            </div>
          ),
        },
        {
          accessorKey: "subscribedAt",
          header: "تاريخ الاشتراك",
          cell: ({ row }) => (
            <div className="flex items-center gap-2">
              <Calendar className="size-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {new Date(row.original.subscribedAt).toLocaleDateString('ar-SA', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          ),
        },
        {
          accessorKey: "unsubscribedAt",
          header: "تاريخ إلغاء الاشتراك",
          cell: ({ row }) => (
            <div className="text-sm text-gray-600">
              {row.original.unsubscribedAt ? (
                <div className="flex items-center gap-2">
                  <Calendar className="size-4 text-gray-500" />
                  {new Date(row.original.unsubscribedAt).toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </div>
          ),
        },
        {
          id: "actions",
          header: "الإجراءات",
          cell: ({ row }) => (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleToggleStatus(row.original)}
                className="flex items-center gap-1"
              >
                {row.original.isActive ? (
                  <>
                    <ToggleLeft className="size-4" />
                    إلغاء التفعيل
                  </>
                ) : (
                  <>
                    <ToggleRight className="size-4" />
                    تفعيل
                  </>
                )}
              </Button>
            </div>
          ),
        },
      ]}
      searchableColumns={[
        {
          id: "email",
          title: "البريد الإلكتروني",
        },
      ]}
      filterableColumns={[
        {
          id: "isActive",
          title: "الحالة",
          options: [
            { label: "نشط", value: "true" },
            { label: "غير نشط", value: "false" },
          ],
        },
      ]}
      title="إدارة المشتركين في النشرة البريدية"
      description="عرض وإدارة قائمة المشتركين في النشرة البريدية"
    />
  )
}
