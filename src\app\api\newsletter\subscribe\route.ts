import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import prisma from "@/lib/prisma"
import { sendNewsletterWelcomeEmail } from "@/app/(website)/newsletter/_lib/newsletter-emails"

// Schema للتحقق من صحة البيانات
const subscribeSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صحيح"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = subscribeSchema.parse(body)
    const { email } = validatedData

    // التحقق من عدم وجود البريد مسبقاً
    const existingSubscriber = await prisma.newsletter.findUnique({
      where: { email }
    })

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        return NextResponse.json(
          { 
            success: false, 
            message: "هذا البريد الإلكتروني مشترك بالفعل في النشرة البريدية" 
          },
          { status: 400 }
        )
      } else {
        // إعادة تفعيل الاشتراك إذا كان ملغياً
        await prisma.newsletter.update({
          where: { email },
          data: {
            isActive: true,
            subscribedAt: new Date(),
            unsubscribedAt: null
          }
        })

        // إرسال بريد الترحيب
        await sendNewsletterWelcomeEmail(email)

        return NextResponse.json({
          success: true,
          message: "تم تجديد اشتراكك في النشرة البريدية بنجاح!"
        })
      }
    }

    // إنشاء اشتراك جديد
    await prisma.newsletter.create({
      data: { email }
    })

    // إرسال بريد الترحيب
    await sendNewsletterWelcomeEmail(email)

    return NextResponse.json({
      success: true,
      message: "تم الاشتراك في النشرة البريدية بنجاح! سنرسل لك أحدث المقالات والأخبار."
    })

  } catch (error) {
    console.error("Newsletter subscription error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: error.errors[0]?.message || "البيانات المدخلة غير صحيحة" 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        message: "حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى." 
      },
      { status: 500 }
    )
  }
}
