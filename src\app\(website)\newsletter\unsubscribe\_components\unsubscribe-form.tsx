"use client"

import React, { useState, useTransition } from "react"
import { Mail, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { unsubscribeFromNewsletter } from "../../_lib/newsletter-actions"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface UnsubscribeFormProps {
  email?: string
  showConfirmation?: boolean
}

export default function UnsubscribeForm({ 
  email: initialEmail, 
  showConfirmation = false 
}: UnsubscribeFormProps) {
  const [email, setEmail] = useState(initialEmail || "")
  const [result, setResult] = useState<{
    success: boolean
    message: string
  } | null>(null)
  const [isPending, startTransition] = useTransition()

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      try {
        const response = await unsubscribeFromNewsletter(formData)
        setResult(response)
      } catch (error) {
        setResult({
          success: false,
          message: "حدث خطأ غير متوقع"
        })
      }
    })
  }

  // إذا تم إلغاء الاشتراك بنجاح، عرض رسالة النجاح فقط
  if (result?.success) {
    return (
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <CheckCircle className="size-16 text-green-600" />
        </div>
        <h2 className="text-xl font-bold text-green-800">
          تم إلغاء الاشتراك بنجاح
        </h2>
        <p className="text-green-700">
          {result.message}
        </p>
        <div className="pt-4">
          <p className="text-sm text-gray-600">
            شكراً لك على الوقت الذي قضيته معنا. نتمنى لك كل التوفيق!
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {showConfirmation && (
        <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <AlertTriangle className="size-5 text-yellow-600 mt-0.5 shrink-0" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800">تأكيد إلغاء الاشتراك</p>
            <p className="text-yellow-700 mt-1">
              بإلغاء الاشتراك، لن تصلك المزيد من النشرات البريدية والتحديثات من أكاديمية د. ناهد باشطح.
            </p>
          </div>
        </div>
      )}

      <form action={handleSubmit} className="space-y-4">
        {!initialEmail && (
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني
            </label>
            <div className="relative">
              <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 size-5 text-gray-400" />
              <Input
                id="email"
                name="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل بريدك الإلكتروني"
                required
                disabled={isPending}
                className="pr-10"
                dir="ltr"
              />
            </div>
          </div>
        )}

        {initialEmail && (
          <input type="hidden" name="email" value={initialEmail} />
        )}

        <Button
          type="submit"
          disabled={isPending || (!initialEmail && !email.trim())}
          variant="destructive"
          className="w-full"
        >
          {isPending ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
              جاري المعالجة...
            </div>
          ) : (
            showConfirmation ? "تأكيد إلغاء الاشتراك" : "إلغاء الاشتراك"
          )}
        </Button>
      </form>

      {result && !result.success && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-3">
            <XCircle className="size-5 text-red-600 mt-0.5 shrink-0" />
            <div>
              <p className="font-medium text-red-800">حدث خطأ</p>
              <p className="text-sm text-red-700 mt-1">
                {result.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {!showConfirmation && (
        <div className="text-center">
          <p className="text-xs text-gray-500">
            إذا كنت تواجه مشاكل، يمكنك التواصل معنا مباشرة
          </p>
        </div>
      )}
    </div>
  )
}
