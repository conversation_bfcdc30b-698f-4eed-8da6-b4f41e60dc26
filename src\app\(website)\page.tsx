import * as React from "react"
import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import heroImagePhone from "@/../public/home-files/hero-image-phone.jpg"
import heroImage from "@/../public/home-files/hero-image.jpg"
import { ArrowLeftIcon, ChevronLeftIcon } from "lucide-react"

import { getHomeCourses, getSiteSettings, getTestimonials } from "@/lib/querys"
import { Button } from "@/components/ui/button"
import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers"
import { Skeleton } from "@/components/ui/skeleton"
import {
  DividerShadow_X,
  DividerShadow_Y,
  HeroImageFrame,
  HeroImageFramePhone,
  QuoteIcon,
  QuoteMyStory,
  ServicesConsultingIcon,
  ServicesShortProgramsIcon,
  ServicesTherapySessionsIcon,
  ServicesTreatmentProgramsIcon,
} from "@/components/ui/svg/icons"
import { LogoLarge } from "@/components/ui/svg/logos"
import TitleSection from "@/components/ui/title-section"
import { StoryShareVideo, StoryVideo } from "@/components/story-video"

import { CourseCard } from "./courses/_components/course-card"
import { NewsletterSection } from "@/components/newsletter/newsletter-section"

export const dynamic = "force-static"

export const metadata: Metadata = {
  title: "اكاديمية د.ناهد باشطح",
  description:
    "مرحبًا بك في عالم العلاج الشعوري ،نحن هنا لمساعدة القادة ونخب المجتمع والمشاهير على تحرير مشاعرهم السلبية، وتجاوز ضغوط الأضواء والمسؤوليات.",
}

// ================================================================
// الصفحة الرئيسية
// ================================================================
export default function HomePage() {
  return (
    <div>
      <Hero />
      <Services />
      <Story />
      <React.Suspense fallback={<div>جاري التحميل ...</div>}>
        <SomeCourses />
      </React.Suspense>
      <NewsletterSection />
      <React.Suspense fallback={<div>جاري التحميل ...</div>}>
        <CustomersTestimonials />
      </React.Suspense>
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// # مكون الهيرو
// --------------------------------------------------------------------------------------
//

function Hero() {
  return (
    <div className="relative flex w-full flex-col overflow-clip max-lg:pb-28 lg:flex-row">
      {/* صورة الهيرو الخاصة بالشاشات الصغيرة */}
      <div className="relative mt-12 w-full overflow-clip lg:hidden">
        <div className="relative aspect-[1/0.9] w-[105%]">
          <Image
            placeholder="blur"
            fetchPriority="high"
            src={heroImagePhone}
            alt="الدكتورة ناهد باشطح"
            className="border-x-secondary border-y-background h-auto w-full border-2 lg:hidden"
          />
        </div>
        <HeroImageFramePhone className="absolute top-0 h-auto w-full" />
      </div>

      {/* نصوص الهيرو */}
      <div className="relative z-20 flex max-w-full flex-1 flex-col items-center px-6 text-balance max-lg:min-h-96 max-lg:justify-center lg:w-[60%] lg:pt-28">
        <div className="relative flex w-full max-w-xl flex-col items-center justify-center text-center">
          {/* صورة الشعار الخاص بالشاشات الكبيرة */}
          <LogoLarge className="hidden lg:block" />
          <h1 className="bg-primary-gradient-x mt-5 bg-clip-text py-1 text-[40px] font-bold text-transparent drop-shadow-[0_4px_2px_#00000050]">
            اكاديمية د.ناهد باشطح
          </h1>
          <p className="mt-2 text-lg font-bold drop-shadow-[0_2px_3px_#00000030] lg:text-xl">
            خبيرة في دعم جودة الحياة للقيادات وصناع القرار.
          </p>
          <p className="mt-7 w-full text-center text-base text-balance">
            مرحبًا بك في عالم العلاج الشعوري ،نحن هنا لمساعدة القادة ونخب
            المجتمع والمشاهير على تحرير مشاعرهم السلبية، وتجاوز ضغوط الأضواء
            والمسؤوليات.
          </p>
          <div className="mt-8 flex gap-4">
            <div>
              <Button asChild>
                <Link href="/consultation">
                  حجز استشارة <ChevronLeftIcon />
                </Link>
              </Button>
            </div>
            <div>
              <Button variant="outline" asChild>
                <Link href="/about-me">
                  نبذة عني <ChevronLeftIcon />
                </Link>
              </Button>
            </div>
          </div>
          <DividerShadow_Y className="pointer-events-none absolute -top-14 -right-20 scale-y-90 object-fill select-none sm:-top-28 sm:scale-75 lg:top-1 lg:scale-y-100" />
          <DividerShadow_Y className="pointer-events-none absolute -top-14 -left-20 scale-y-90 rotate-180 select-none sm:-top-28 sm:scale-75 lg:top-1 lg:scale-y-100" />
        </div>
      </div>

      {/* صورة الهيرو الخاصة بالشاشات الكبيرة */}
      <div className="bg-secondary relative hidden h-[750px] w-[514px] lg:block">
        <Image
          src={heroImage}
          className="hidden h-full w-auto -translate-x-10 lg:block"
          placeholder="blur"
          fetchPriority="high"
          alt="الموقع الرسمي للدكتورة ناهد باشطح"
        />
        <HeroImageFrame className="absolute top-0 hidden lg:block" />
      </div>
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// # مكون الخدمات
// --------------------------------------------------------------------------------------
//

const items = [
  {
    title: "خدمة استشارية",
    description:
      "نوفر حلولاً استشارية عاطفية مخصصة تُقدّم بسرية تامة، لتلبية احتياجات الشخصيات البارزة الباحثة عن التميز والخصوصية.",
    Icon: ServicesConsultingIcon,
  },
  {
    title: "برامج تدريبية",
    description:
      "دورات تدريبية مكثفة وفعالة تركز على تطوير المهارات الشخصية والمهنية في وقت قصير، مع تحقيق نتائج ملموسة.",
    Icon: ServicesShortProgramsIcon,
  },
  {
    title: "جلسات علاجية",
    description:
      "جلسات علاجية مميزة تُقدم في المنزل أو أثناء السفر، مع التركيز على تقديم تجربة فاخرة تضمن الراحة والخصوصية.",
    Icon: ServicesTherapySessionsIcon,
  },

  {
    title: "برامج علاجية",
    description:
      "برامج علاجية مرنة ومكثفة مصممة لتناسب جداول الشخصيات القيادية والمجتمعية المزدحمة، مع ضمان تقديم نتائج فعّالة.",
    Icon: ServicesTreatmentProgramsIcon,
  },
]

function Services() {
  return (
    <div className="relative overflow-x-clip max-sm:mt-16">
      <div className="bg-primary-gradient-x absolute -top-5 right-0 h-12 w-60 rounded-l-full lg:-top-7 lg:h-14 lg:w-96" />
      <div className="bg-primary-gradient-x absolute -bottom-5 left-0 h-12 w-60 rounded-r-full lg:-bottom-7 lg:h-14 lg:w-96" />
      <div className="divide-muted/10 bg-background relative grid grid-cols-1 place-content-center gap-10 rounded-md px-6 py-28 shadow-[0_0_25px_#00000030] max-sm:divide-y sm:grid-cols-2 lg:grid-cols-4">
        {items.map((item, index) => {
          return <ServicesCard {...item} key={index} />
        })}
      </div>
    </div>
  )
}

function ServicesCard({
  title,
  description,
  Icon,
}: {
  title: string
  description: string
  Icon: React.FunctionComponent<{ className?: string }>
}) {
  return (
    <div className="flex flex-col items-center gap-7 text-center text-balance max-md:py-10">
      <div className="rounded-full shadow-[0_0_18px_#00000025]">
        <Icon />
      </div>
      <div className="flex flex-col items-center gap-3">
        <p className="bg-primary-gradient-x max-w-min bg-clip-text text-2xl font-bold text-nowrap text-transparent">
          {title}
        </p>
        <p className="font-light">{description}</p>
      </div>
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// # مكون قصتي مع العلاج الشعوري
// --------------------------------------------------------------------------------------
//

async function Story() {
  const siteSettings = getSiteSettings()

  return (
    <div className="bg-secondary relative flex w-full flex-col overflow-clip px-3 py-24 lg:flex-row-reverse lg:items-center lg:justify-center lg:px-6">
      <DividerRedTop />

      {/* الدائرة */}
      <div className="bg-primary-gradient-y absolute -right-32 -bottom-5 size-72 rounded-full opacity-50 blur-xs lg:-top-8 lg:bottom-auto lg:size-96"></div>

      {/* الإقتباس */}
      <div className="relative flex items-center justify-center">
        <div className="text-background absolute w-full max-w-80 space-y-3 px-8 text-center sm:max-w-sm sm:px-4 lg:px-6">
          <p className="text-lg font-bold drop-shadow-lg">
            قصتي مع العلاج الشعوري
          </p>
          <p className="pb-2 text-sm font-light">
            “بخبرة تمتد لأكثر من ثلاثة عقود في الإعلام، انتقلتُ بشغف إلى مجال
            العلاج الشعوري لمساعدة الأفراد على تحسين جودة حياتهم.”
          </p>
        </div>
        <QuoteMyStory className="" />
      </div>

      {/* الضل الفاصل */}
      <DividerShadow_X className="mx-auto w-full scale-x-125 scale-y-150 mix-blend-overlay lg:hidden" />
      <DividerShadow_Y className="hidden h-[410px] w-auto scale-y-150 opacity-25 mix-blend-overlay lg:block" />

      {/* الفيديو */}
      <div>
        <div className="bg-background/[0.01] mt-6 rounded-xl p-3 shadow-[0_25px_20px_#00000040] backdrop-blur-2xl lg:max-w-(--breakpoint-sm) lg:shadow-[60px_0_20px_#00000040]">
          <div className="aspect-video">
            <React.Suspense
              fallback={
                <Skeleton className="h-full w-full animate-pulse rounded-xl" />
              }
            >
              <StoryVideo siteSettings={siteSettings} />
            </React.Suspense>
          </div>
          <StoryShareVideo />
        </div>
      </div>
      <DividerRedBottom />
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// # مكون بعض الدورات التدريبية
// --------------------------------------------------------------------------------------
//

async function SomeCourses() {
  const courses = await getHomeCourses()

  if (!courses || !courses.length) return null

  return (
    <div className="mt-20 flex flex-col gap-24 rounded-xl pt-28 shadow-[0_0px_15px_-4px_rgb(0_0_0/0.4)]">
      <TitleSection
        title={<h2>دورات تدريبية متخصصة</h2>}
        description="مجموعة دورات شاملة لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات، من خلال تقنيات فعّالة لتحقيق التوازن النفسي والجسدي"
      />
      {courses.map((course) => (
        <CourseCard key={course.id} {...course} />
      ))}
      <div className="bg-background/5 mx-auto -mb-6 w-fit rounded-lg px-5 py-3 backdrop-blur-sm">
        <Link
          className="group text-primary flex items-center gap-2 py-0.5 underline hover:opacity-90"
          href="/courses"
        >
          شاهد المزيد
          <ArrowLeftIcon className="mt-0.5 size-[18px] duration-200 group-hover:-translate-x-1.5" />
        </Link>
      </div>
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// # مكون شهادات العملاء
// --------------------------------------------------------------------------------------
//

async function CustomersTestimonials() {
  const testimonials = await getTestimonials()
  if (!testimonials || !testimonials.length) return null

  return (
    <div className="py-32 md:py-44">
      <TitleSection
        title={<h2>شهادات عملاء سابقين</h2>}
        description="نعتز بثقة عملائنا ونفخر بمشاركتكم تجاربهم معنا. إليكم بعض الشهادات من أشخاص خاضوا رحلتهم العلاجية تحت إشراف الدكتورة ناهد باشطح، حيث لمسوا الفرق وشعروا بالتحسن بفضل الرعاية المهنية والاهتمام الشخصي."
        classDescription="max-sm:text-sm max-sm:leading-relaxed"
        classContainer="lg:max-w-4xl"
      />
      <div className="flex items-center justify-center gap-14 py-12 text-center text-sm leading-loose max-sm:flex-col sm:flex-wrap md:gap-16 md:py-20">
        {testimonials.map(({ id, testimony }) => (
          <div
            key={id}
            className="bg-secondary text-background relative flex aspect-square max-w-72 items-center justify-center rounded-tl-lg rounded-tr-3xl rounded-br-lg rounded-bl-3xl px-7 py-10 shadow-lg shadow-black/40"
          >
            <QuoteIcon className="absolute -top-6 right-5 size-16" />
            <QuoteIcon className="absolute right-[calc(50%-20px)] -bottom-6 size-16 origin-center rotate-180" />
            <p className="line-clamp-6">{testimony}</p>
          </div>
        ))}
      </div>
    </div>
  )
}
