import "server-only"

import { createEmailTransporter } from "@/lib/email-transporter"
import { siteName } from "@/configs/site"

/**
 * إرسال بريد تأكيد الاشتراك في النشرة البريدية
 */
export async function sendNewsletterWelcomeEmail(email: string) {
  try {
    const transporter = createEmailTransporter()

    const emailHtml = generateWelcomeEmailTemplate(email)

    const info = await transporter.sendMail({
      to: email,
      html: emailHtml,
      subject: `مرحباً بك في النشرة البريدية - ${siteName}`,
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, info }
  } catch (error) {
    console.error("Newsletter welcome email error:", error)
    return { isSended: false, error }
  }
}

/**
 * إرسال بريد تأكيد إلغاء الاشتراك
 */
export async function sendNewsletterGoodbyeEmail(email: string) {
  try {
    const transporter = createEmailTransporter()

    const emailHtml = generateGoodbyeEmailTemplate(email)

    const info = await transporter.sendMail({
      to: email,
      html: emailHtml,
      subject: `تأكيد إلغاء الاشتراك - ${siteName}`,
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, info }
  } catch (error) {
    console.error("Newsletter goodbye email error:", error)
    return { isSended: false, error }
  }
}

/**
 * إرسال النشرة البريدية لجميع المشتركين
 */
export async function sendNewsletterToSubscribers(
  subject: string,
  content: string,
  subscriberEmails: string[]
) {
  try {
    const transporter = createEmailTransporter()
    const results = []

    // إرسال النشرة لكل مشترك على حدة لتجنب مشاكل الخصوصية
    for (const email of subscriberEmails) {
      try {
        const emailHtml = generateNewsletterTemplate(content, email)

        const info = await transporter.sendMail({
          to: email,
          html: emailHtml,
          subject: `${subject} - ${siteName}`,
          from: process.env.SMTP_FROM || "<EMAIL>",
        })

        results.push({ email, success: true, info })
      } catch (error) {
        console.error(`Failed to send newsletter to ${email}:`, error)
        results.push({ email, success: false, error })
      }
    }

    return { 
      success: true, 
      results,
      totalSent: results.filter(r => r.success).length,
      totalFailed: results.filter(r => !r.success).length
    }
  } catch (error) {
    console.error("Newsletter bulk send error:", error)
    return { success: false, error }
  }
}

/**
 * قالب بريد الترحيب بالمشترك الجديد
 */
function generateWelcomeEmailTemplate(email: string): string {
  const unsubscribeUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/newsletter/unsubscribe?email=${encodeURIComponent(email)}`
  
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>مرحباً بك في النشرة البريدية</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .unsubscribe { font-size: 12px; color: #999; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>مرحباً بك في النشرة البريدية!</h1>
          <p>أكاديمية د. ناهد باشطح</p>
        </div>
        <div class="content">
          <h2>شكراً لك على الاشتراك!</h2>
          <p>نحن سعداء جداً لانضمامك إلى مجتمعنا. ستصلك أحدث المقالات والنصائح والأخبار المتعلقة بالعلاج الشعوري والتطوير الشخصي.</p>
          
          <h3>ماذا ستحصل عليه:</h3>
          <ul>
            <li>مقالات حصرية في العلاج الشعوري</li>
            <li>نصائح عملية للتطوير الشخصي</li>
            <li>إشعارات بالدورات والبرامج الجديدة</li>
            <li>محتوى مفيد ومُلهم بشكل دوري</li>
          </ul>
          
          <p>نتطلع إلى مشاركة رحلة التطوير والنمو معك!</p>
          
          <div class="unsubscribe">
            <p>إذا كنت تريد إلغاء الاشتراك في أي وقت، يمكنك <a href="${unsubscribeUrl}">النقر هنا</a></p>
          </div>
        </div>
        <div class="footer">
          <p>© 2024 أكاديمية د. ناهد باشطح - جميع الحقوق محفوظة</p>
          <p>هذا البريد تم إرساله إلى: ${email}</p>
        </div>
      </div>
    </body>
    </html>
  `
}

/**
 * قالب بريد تأكيد إلغاء الاشتراك
 */
function generateGoodbyeEmailTemplate(email: string): string {
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تأكيد إلغاء الاشتراك</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: #6c757d; color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>تم إلغاء اشتراكك</h1>
          <p>أكاديمية د. ناهد باشطح</p>
        </div>
        <div class="content">
          <h2>نأسف لرؤيتك تغادر</h2>
          <p>تم إلغاء اشتراكك في النشرة البريدية بنجاح. لن تصلك المزيد من الرسائل الإلكترونية منا.</p>
          
          <p>إذا غيرت رأيك في المستقبل، يمكنك دائماً الاشتراك مرة أخرى من خلال موقعنا الإلكتروني.</p>
          
          <p>نشكرك على الوقت الذي قضيته معنا، ونتمنى لك كل التوفيق في رحلتك!</p>
        </div>
        <div class="footer">
          <p>© 2024 أكاديمية د. ناهد باشطح - جميع الحقوق محفوظة</p>
          <p>هذا البريد تم إرساله إلى: ${email}</p>
        </div>
      </div>
    </body>
    </html>
  `
}

/**
 * قالب النشرة البريدية
 */
function generateNewsletterTemplate(content: string, email: string): string {
  const unsubscribeUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/newsletter/unsubscribe?email=${encodeURIComponent(email)}`
  
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>النشرة البريدية</title>
      <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .unsubscribe { font-size: 12px; color: #999; margin-top: 20px; border-top: 1px solid #eee; padding-top: 15px; }
        .content h1, .content h2, .content h3 { color: #333; }
        .content img { max-width: 100%; height: auto; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>النشرة البريدية</h1>
          <p>أكاديمية د. ناهد باشطح</p>
        </div>
        <div class="content">
          ${content}
          
          <div class="unsubscribe">
            <p>إذا كنت لا تريد استقبال هذه الرسائل، يمكنك <a href="${unsubscribeUrl}">إلغاء الاشتراك هنا</a></p>
          </div>
        </div>
        <div class="footer">
          <p>© 2024 أكاديمية د. ناهد باشطح - جميع الحقوق محفوظة</p>
          <p>هذا البريد تم إرساله إلى: ${email}</p>
        </div>
      </div>
    </body>
    </html>
  `
}
