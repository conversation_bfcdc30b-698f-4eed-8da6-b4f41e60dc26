# نظام النشرة البريدية

نظام شامل لإدارة النشرة البريدية في موقع أكاديمية د. ناهد باشطح.

## المميزات

### للمستخدمين
- ✅ الاشتراك في النشرة البريدية من Footer أو الصفحة الرئيسية
- ✅ بريد ترحيب تلقائي عند الاشتراك
- ✅ إمكانية إلغاء الاشتراك بسهولة
- ✅ بريد تأكيد عند إلغاء الاشتراك
- ✅ حماية من الاشتراك المكرر

### للإدارة
- ✅ عرض قائمة المشتركين مع الإحصائيات
- ✅ إرسال نشرات بريدية لجميع المشتركين
- ✅ تفعيل/إلغاء تفعيل المشتركين
- ✅ حذف المشتركين
- ✅ قوالب HTML جذابة للنشرات

## بنية الملفات

```
src/app/(website)/newsletter/
├── _lib/
│   ├── newsletter-actions.ts      # Server Actions
│   └── newsletter-emails.ts       # دوال إرسال البريد
├── unsubscribe/
│   ├── page.tsx                   # صفحة إلغاء الاشتراك
│   └── _components/
│       └── unsubscribe-form.tsx   # نموذج إلغاء الاشتراك
└── README.md

src/components/newsletter/
├── newsletter-subscription.tsx     # مكون الاشتراك
└── newsletter-section.tsx         # قسم النشرة في الصفحة الرئيسية

src/app/(dashboard)/admin/newsletter/
├── page.tsx                       # صفحة إدارة المشتركين
└── send/
    └── page.tsx                   # صفحة إرسال النشرات

src/components/dashboard-workspace/newsletter-CRUD/
├── NewsletterTable.tsx            # جدول المشتركين
└── NewsletterSendForm.tsx         # نموذج إرسال النشرة
```

## قاعدة البيانات

### جدول Newsletter
```sql
model Newsletter {
  id             String    @id @default(uuid())
  email          String    @unique
  isActive       Boolean   @default(true)
  subscribedAt   DateTime  @default(now())
  unsubscribedAt DateTime?
  
  @@index([email])
  @@index([isActive, subscribedAt(sort: Desc)])
}
```

## Server Actions

### `subscribeToNewsletter(formData: FormData)`
- الاشتراك في النشرة البريدية
- التحقق من عدم وجود البريد مسبقاً
- إرسال بريد ترحيب

### `unsubscribeFromNewsletter(formData: FormData)`
- إلغاء الاشتراك من النشرة البريدية
- إرسال بريد تأكيد الإلغاء

### `getNewsletterStats()`
- جلب إحصائيات المشتركين

### `getNewsletterSubscribers(page, limit)`
- جلب قائمة المشتركين مع التصفح

## دوال البريد الإلكتروني

### `sendNewsletterWelcomeEmail(email: string)`
- إرسال بريد ترحيب للمشترك الجديد

### `sendNewsletterGoodbyeEmail(email: string)`
- إرسال بريد تأكيد إلغاء الاشتراك

### `sendNewsletterToSubscribers(subject, content, emails)`
- إرسال النشرة البريدية لجميع المشتركين

## المكونات

### `NewsletterSubscription`
مكون الاشتراك الرئيسي مع خيارات متعددة:
- `variant`: "default" | "footer" | "inline"
- `showTitle`: إظهار/إخفاء العنوان
- `title`: عنوان مخصص
- `description`: وصف مخصص

### `FooterNewsletterSubscription`
مكون مخصص للـ Footer

### `NewsletterSection`
قسم كامل للنشرة البريدية في الصفحة الرئيسية

## الاستخدام

### في Footer
```tsx
import { FooterNewsletterSubscription } from "@/components/newsletter/newsletter-subscription"

<FooterNewsletterSubscription />
```

### في الصفحة الرئيسية
```tsx
import { NewsletterSection } from "@/components/newsletter/newsletter-section"

<NewsletterSection />
```

### مكون بسيط
```tsx
import { QuickNewsletterSubscription } from "@/components/newsletter/newsletter-subscription"

<QuickNewsletterSubscription />
```

## الروابط

- **الاشتراك**: يتم من خلال النماذج في الموقع
- **إلغاء الاشتراك**: `/newsletter/unsubscribe?email=<EMAIL>`
- **إدارة المشتركين**: `/admin/newsletter`
- **إرسال النشرات**: `/admin/newsletter/send`

## متطلبات البيئة

تأكد من وجود متغيرات البيئة التالية:
```env
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password
SMTP_FROM=<EMAIL>
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## الأمان

- ✅ التحقق من صحة البريد الإلكتروني
- ✅ حماية من الاشتراك المكرر
- ✅ Server Actions آمنة
- ✅ روابط إلغاء الاشتراك آمنة
- ✅ عدم مشاركة قوائم البريد الإلكتروني

## التطوير المستقبلي

- [ ] إضافة تصنيفات للنشرات
- [ ] جدولة إرسال النشرات
- [ ] تتبع معدلات الفتح والنقر
- [ ] قوالب متعددة للنشرات
- [ ] تصدير قوائم المشتركين
- [ ] إحصائيات متقدمة
