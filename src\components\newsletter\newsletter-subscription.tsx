"use client"

import React, { useState, useTransition } from "react"
import { Mail, Send, CheckCircle, AlertCircle } from "lucide-react"
import { subscribeToNewsletter } from "@/app/(website)/newsletter/_lib/newsletter-actions"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface NewsletterSubscriptionProps {
  className?: string
  variant?: "default" | "footer" | "inline"
  showTitle?: boolean
  title?: string
  description?: string
}

export function NewsletterSubscription({
  className = "",
  variant = "default",
  showTitle = true,
  title = "اشترك في النشرة البريدية",
  description = "احصل على أحدث المقالات والنصائح في العلاج الشعوري والتطوير الشخصي"
}: NewsletterSubscriptionProps) {
  const [email, setEmail] = useState("")
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null)
  const [isPending, startTransition] = useTransition()

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      try {
        const result = await subscribeToNewsletter(formData)
        
        if (result.success) {
          setMessage({ type: "success", text: result.message })
          setEmail("") // مسح الحقل عند النجاح
        } else {
          setMessage({ type: "error", text: result.message })
        }
      } catch (error) {
        setMessage({ 
          type: "error", 
          text: "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى." 
        })
      }
    })
  }

  // تصميم مختلف حسب المتغير
  const getContainerClasses = () => {
    const baseClasses = "w-full"
    
    switch (variant) {
      case "footer":
        return `${baseClasses} max-w-sm`
      case "inline":
        return `${baseClasses} max-w-md mx-auto`
      default:
        return `${baseClasses} max-w-lg mx-auto p-6 bg-white rounded-lg shadow-md`
    }
  }

  const getInputClasses = () => {
    switch (variant) {
      case "footer":
        return "h-12 text-sm"
      default:
        return "h-12"
    }
  }

  const getButtonClasses = () => {
    switch (variant) {
      case "footer":
        return "h-12 px-4"
      default:
        return "h-12 px-6"
    }
  }

  return (
    <div className={`${getContainerClasses()} ${className}`}>
      {showTitle && (
        <div className="mb-4">
          <h3 className={`font-bold flex items-center gap-2 ${
            variant === "footer" ? "text-background text-lg" : "text-primary text-xl"
          }`}>
            <Mail className="size-5" />
            {title}
          </h3>
          {description && (
            <p className={`mt-2 text-sm ${
              variant === "footer" ? "text-background/80" : "text-muted-foreground"
            }`}>
              {description}
            </p>
          )}
        </div>
      )}

      <form action={handleSubmit} className="space-y-3">
        <div className="flex gap-2">
          <Input
            type="email"
            name="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="أدخل بريدك الإلكتروني"
            required
            disabled={isPending}
            className={getInputClasses()}
            dir="ltr"
          />
          <Button
            type="submit"
            disabled={isPending || !email.trim()}
            className={getButtonClasses()}
            variant={variant === "footer" ? "outlineDark" : "default"}
          >
            {isPending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
            ) : (
              <>
                <Send className="size-4" />
                اشترك
              </>
            )}
          </Button>
        </div>

        {message && (
          <div className={`flex items-center gap-2 p-3 rounded-md text-sm ${
            message.type === "success" 
              ? "bg-green-50 text-green-700 border border-green-200" 
              : "bg-red-50 text-red-700 border border-red-200"
          }`}>
            {message.type === "success" ? (
              <CheckCircle className="size-4 shrink-0" />
            ) : (
              <AlertCircle className="size-4 shrink-0" />
            )}
            <span>{message.text}</span>
          </div>
        )}
      </form>

      {variant === "footer" && (
        <p className="text-xs text-background/60 mt-2">
          لن نشارك بريدك الإلكتروني مع أي طرف ثالث
        </p>
      )}
    </div>
  )
}

// مكون مبسط للاستخدام السريع
export function QuickNewsletterSubscription({ className }: { className?: string }) {
  return (
    <NewsletterSubscription
      variant="inline"
      className={className}
      showTitle={false}
    />
  )
}

// مكون للـ Footer
export function FooterNewsletterSubscription({ className }: { className?: string }) {
  return (
    <NewsletterSubscription
      variant="footer"
      className={className}
      title="النشرة البريدية"
      description="اشترك ليصلك كل جديد"
    />
  )
}
