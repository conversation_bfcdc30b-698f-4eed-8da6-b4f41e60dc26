"use client"

import { ReactNode } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { LogIn, LogOut, Settings, User, UserPlus2 } from "lucide-react"
import { toast } from "sonner"

import { logOutAction } from "@/lib/actions"
import { cn } from "@/lib/utils"
import { useScrollThreshold } from "@/hooks/useScrollThreshold"
import useSession from "@/hooks/useSession"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { Skeleton } from "../ui/skeleton"

export function NavContainer({ children }: { children: ReactNode }) {
  const isScroll = useScrollThreshold(70)

  return (
    <header className="fixed top-0 right-0 left-0 z-40 flex max-h-min items-center justify-center lg:group-data-[scroll-locked=1]/body:right-[10px]">
      <nav
        className={cn(
          "border-border/0 flex w-full max-w-(--breakpoint-2xl) items-center justify-between rounded-b-lg transition-all duration-500",
          isScroll &&
            "border-border/10 bg-background/90 shadow-[0px_2px_10px_-1px_#00000020] backdrop-blur-md backdrop-brightness-125"
        )}
      >
        {children}
      </nav>
    </header>
  )
}

export function NavItemContainer({
  itemPath,
  children,
}: {
  children: ReactNode
  itemPath: string
}) {
  const currentPath = usePathname()

  const isEqualPath =
    currentPath === "/"
      ? currentPath === itemPath
      : currentPath.startsWith(
          `/${itemPath
            .split("/")
            .filter((e) => e !== "")
            .at(0)}`
        ) && itemPath !== "/"

  return (
    <div
      data-path={isEqualPath}
      className={cn(
        "group peer bg-secondary hover:bg-primary-gradient-x relative max-w-fit bg-clip-text text-nowrap text-transparent",
        isEqualPath && "bg-primary-gradient-x drop-shadow-md"
      )}
    >
      {children}
      <div
        className={cn(
          "bg-primary-gradient-x pointer-events-none absolute -bottom-1.5 hidden h-0.5 w-0 rounded-full transition-all duration-200 group-hover:w-full group-hover:opacity-100 md:block",
          isEqualPath ? "w-full opacity-100" : "opacity-30"
        )}
      />
    </div>
  )
}

export function UserAvatar() {
  const session = useSession()

  return (
    <div>
      <DropdownMenu dir="rtl">
        <DropdownMenuTrigger
          disabled={session === null}
          className="ml-3 rounded-full md:ml-6"
        >
          {session === null ? (
            <Skeleton className="size-10 rounded-full" />
          ) : (
            <Avatar name={session?.name} />
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {session ? (
            <>
              <DropdownMenuLabel className="truncate">
                {session.name}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile/personal-info">
                  الإعدادات
                  <DropdownMenuShortcut>
                    <Settings />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:bg-destructive/10 focus:text-destructive"
                onClick={async () => {
                  await logOutAction()
                  toast.success("تم تسجيل الخروج بنجاح")
                  window.location.reload()
                }}
              >
                تسجيل الخروج
                <DropdownMenuShortcut>
                  <LogOut />
                </DropdownMenuShortcut>
              </DropdownMenuItem>
            </>
          ) : (
            <>
              <DropdownMenuItem asChild>
                <Link href="/auth/login">
                  تسجيل الدخول
                  <DropdownMenuShortcut>
                    <LogIn />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/auth/signup">
                  إنشاء حساب
                  <DropdownMenuShortcut>
                    <UserPlus2 />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

function Avatar({ name }: { name?: string }) {
  return (
    <div>
      <div className="bg-secondary relative flex size-10 shrink-0 items-center justify-center rounded-full stroke-none">
        <span className="sr-only">الحساب الشخصي</span>
        {!name ? (
          <User className="text-background size-5" />
        ) : (
          <>
            <span className="text-background pb-1 text-sm font-bold">
              {name?.at(0)}
            </span>
            <span className="absolute bottom-0 left-0 flex size-3">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-green-400 opacity-75"></span>
              <span className="relative inline-flex size-3 rounded-full bg-green-500"></span>
            </span>
          </>
        )}
      </div>
    </div>
  )
}
