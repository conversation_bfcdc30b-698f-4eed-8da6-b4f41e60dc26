import { Suspense } from "react"
import { Metadata } from "next"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { Article } from "@prisma/client"
import { CalendarDays, ExternalLink } from "lucide-react"

import { getArticles } from "@/lib/querys"
import { Pagination } from "@/components/ui/pagination"
import { Skeleton } from "@/components/ui/skeleton"
import { DividerShadow_X } from "@/components/ui/svg/icons"

type ArticlesProps = {
  searchParams: Promise<{ page?: string }>
}

export const metadata: Metadata = {
  title: `مقالات د. ناهد باشطح`,
  description:
    "مقالات الدكتورة ناهد باشطح في مجال العلاج الشعوري وتقنيات العلاج الشعوري",
  keywords: "مقالات, د. ناهد باشطح, العلاج الشعوري, تقنيات العلاج الشعوري",
}

// ==================================================================================
// صفحة تعرض كل المقالات باستخدام الترقيم الصفحي
// ==================================================================================
export default async function ArticlesPage(props: ArticlesProps) {
  const pageIndex = (await props.searchParams).page || "1"

  return (
    <>
      <div>
        <DividerShadow_X className="h-20 w-full scale-x-150 -scale-y-100 sm:scale-x-250 md:scale-x-400" />
        <div className="bg-secondary grid gap-4 rounded-xl p-4 md:grid-cols-3">
          {/* بنر جريدة الجزيرة */}
          <a
            href="https://www.al-jazirah.com/writers/2025116.html"
            target="_blank"
            rel="noopener noreferrer"
            className="group block w-full rounded-md bg-white p-6 shadow-[0_0_15px_-2px_#00000040] transition-all duration-200 hover:scale-101 hover:shadow-[0_0_20px_-2px_#00000060]"
          >
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-primary group-hover:text-primary/90 text-lg font-bold">
                جريدة الجزيرة
              </h3>
              <ExternalLink className="text-secondary group-hover:text-primary size-5 transition-colors" />
            </div>
            <p className="text-secondary text-sm leading-relaxed">
              اقرأ مقالات الدكتورة ناهد باشطح على موقع جريدة الجزيرة
            </p>
          </a>

          {/* بنر جريدة الرياض */}
          <a
            href="https://www.alriyadh.com/file/301"
            target="_blank"
            rel="noopener noreferrer"
            className="group block w-full rounded-md bg-white p-6 shadow-[0_0_15px_-2px_#00000040] transition-all duration-200 hover:scale-101 hover:shadow-[0_0_20px_-2px_#00000060]"
          >
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-primary group-hover:text-primary/90 text-lg font-bold">
                جريدة الرياض
              </h3>
              <ExternalLink className="text-secondary group-hover:text-primary size-5 transition-colors" />
            </div>
            <p className="text-secondary text-sm leading-relaxed">
              اقرأ مقالات الدكتورة ناهد باشطح على موقع جريدة الرياض
            </p>
          </a>

          {/* بنر جريدة الشرق الأوسط */}
          <a
            href="https://aawsat.com/home/<USER>/105306"
            target="_blank"
            rel="noopener noreferrer"
            className="group block w-full rounded-md bg-white p-6 shadow-[0_0_15px_-2px_#00000040] transition-all duration-200 hover:scale-101 hover:shadow-[0_0_20px_-2px_#00000060]"
          >
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-primary group-hover:text-primary/90 text-lg font-bold">
                الشرق الأوسط
              </h3>
              <ExternalLink className="text-secondary group-hover:text-primary size-5 transition-colors" />
            </div>
            <p className="text-secondary text-sm leading-relaxed">
              اقرأ مقالات الدكتورة ناهد باشطح على موقع جريدة الشرق الأوسط
            </p>
          </a>
        </div>
        <DividerShadow_X className="h-20 w-full scale-x-150 sm:scale-x-250 md:scale-x-400" />
      </div>
      <h2 className="mb-16 text-center text-5xl font-bold">مقالات مختارة</h2>
      <Suspense key={pageIndex} fallback={<ArticlesPageSkeleton />}>
        <ArticlesList pageIndex={pageIndex} />
      </Suspense>
    </>
  )
}

// قمنا بفصل مكون العرض عن الصفحة
// لتجنب حظر العرض حتا يتم حل الوعد وجلب البيانات
// وبدلاً من ذلك سيتم عرض هيكل التحميل من خلال
// Suspense في مكون الصفحة
async function ArticlesList(props: { pageIndex?: string }) {
  const pageIndex = Number(props.pageIndex)
  if (isNaN(pageIndex)) return notFound()

  const { data, pagination } = await getArticles({ pageIndex })

  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>

  return (
    <>
      {data?.map((article) => (
        <ArticleCard key={article.id} article={article} />
      ))}
      <Pagination url="/articles" {...pagination} />
    </>
  )
}

// ==================================================================================
// مكون بطاقة المقالة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleCard({ article }: { article: Article }) {
  return (
    <div className="text-secondary w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/articles/${article.id}#pagination`}
          className="text-primary hover:text-primary/90 text-lg font-bold underline underline-offset-8"
        >
          {article.title}
        </Link>
        <p className="mt-3 line-clamp-3 min-h-[60px] text-sm leading-relaxed">
          {article.seoDescription}
        </p>
      </div>
      <div className="bg-muted/10 w-full rounded-sm px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" /> {formatDate(article.createdAt)}
        </span>
      </div>
    </div>
  )
}

function ArticlesPageSkeleton() {
  const array = Array.from({ length: 10 })

  return array.map((_, i) => (
    <Skeleton key={i} className="h-45 w-full rounded-md" />
  ))
}
