import { revalidatePath } from "next/cache"
import prisma from "@/lib/prisma"
import { sendNewsletterToSubscribers } from "@/app/(website)/newsletter/_lib/newsletter-emails"
import NewsletterSendForm from "@/components/dashboard-workspace/newsletter-CRUD/NewsletterSendForm"

export default async function NewsletterSendPage() {
  // جلب عدد المشتركين النشطين
  const activeSubscribersCount = await prisma.newsletter.count({
    where: { isActive: true }
  })

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* معلومات سريعة */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <h2 className="text-lg font-semibold text-blue-900">إرسال نشرة بريدية جديدة</h2>
        </div>
        <p className="text-blue-700 mt-2">
          سيتم إرسال النشرة إلى <strong>{activeSubscribersCount}</strong> مشترك نشط
        </p>
      </div>

      {/* نموذج إرسال النشرة */}
      <NewsletterSendForm
        activeSubscribersCount={activeSubscribersCount}
        onSend={async (formData) => {
          "use server"

          try {
            const subject = formData.get("subject") as string
            const content = formData.get("content") as string

            if (!subject || !content) {
              return {
                success: false,
                message: "الموضوع والمحتوى مطلوبان"
              }
            }

            // جلب بريد المشتركين النشطين
            const activeSubscribers = await prisma.newsletter.findMany({
              where: { isActive: true },
              select: { email: true }
            })

            if (activeSubscribers.length === 0) {
              return {
                success: false,
                message: "لا يوجد مشتركين نشطين لإرسال النشرة إليهم"
              }
            }

            const subscriberEmails = activeSubscribers.map(s => s.email)

            // إرسال النشرة
            const result = await sendNewsletterToSubscribers(
              subject,
              content,
              subscriberEmails
            )

            if (result.success) {
              // حفظ سجل الإرسال في قاعدة البيانات (اختياري)
              // يمكن إضافة جدول NewsletterCampaign لحفظ تاريخ الإرسال

              revalidatePath("/admin/newsletter/send")

              return {
                success: true,
                message: `تم إرسال النشرة بنجاح إلى ${result.totalSent} مشترك. فشل الإرسال لـ ${result.totalFailed} مشترك.`,
                details: {
                  totalSent: result.totalSent,
                  totalFailed: result.totalFailed,
                  results: result.results
                }
              }
            } else {
              return {
                success: false,
                message: "حدث خطأ أثناء إرسال النشرة البريدية"
              }
            }

          } catch (error) {
            console.error("Newsletter send error:", error)
            return {
              success: false,
              message: "حدث خطأ غير متوقع أثناء إرسال النشرة"
            }
          }
        }}
      />
    </div>
  )
}
