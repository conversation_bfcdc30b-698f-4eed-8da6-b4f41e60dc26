import "server-only"

import { verificationTokenExpiryMinutes } from "@/configs/site"
import { createEmailTransporter } from "@/lib/email-transporter"

export type SendEmailVerificationParams = {
  userName: string
  userEmail: string
}

/**
 * Send an email verification message using SMTP
 */
export async function sendEmailVerification({
  userEmail,
  userName,
}: SendEmailVerificationParams) {
  try {
    const transporter = createEmailTransporter()
    const verificationToken = generateVerificationToken()
    // استخدام المتغير المركزي لتحديد فترة انتهاء صلاحية رابط التحقق
    const verificationTokenExpiry = new Date(
      Date.now() + verificationTokenExpiryMinutes * 60 * 1000
    )
    const verificationUrl = `${process.env.DOMAIN}/auth/verify-email?token=${verificationToken}`

    const emailHtml = generateEmailVerificationTemplate({
      userName,
      verificationUrl,
    })

    const info = await transporter.sendMail({
      to: userEmail,
      html: emailHtml,
      subject: "تأكيد البريد الإلكتروني - أكاديمية د. ناهد باشطح",
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, verificationToken, verificationTokenExpiry, info }
  } catch (error) {
    return { isSended: false, error }
  }
}

/**
 * Generate an email verification HTML template
 */
function generateEmailVerificationTemplate({
  userName,
  verificationUrl,
}: {
  userName: string
  verificationUrl: string
}) {
  return `
    <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h1 style="color: #333; text-align: center;">تأكيد البريد الإلكتروني</h1>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">مرحباً ${userName}،</p>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">شكراً لتسجيلك في أكاديمية د. ناهد باشطح. يرجى النقر على الزر أدناه لتأكيد بريدك الإلكتروني:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">تأكيد البريد الإلكتروني</a>
      </div>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">إذا لم تقم بإنشاء حساب، يمكنك تجاهل هذا البريد الإلكتروني.</p>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">إذا واجهت مشكلة في النقر على الزر، يمكنك نسخ ولصق الرابط التالي في متصفحك:</p>
      <p style="font-size: 14px; line-height: 1.5; color: #777; word-break: break-all;">${verificationUrl}</p>
      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #777; font-size: 12px;">
        <p>أكاديمية د. ناهد باشطح</p>
      </div>
    </div>
  `
}

/**
 * Generate a random token for email verification
 */
function generateVerificationToken(length = 32) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let token = ""

  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return token
}
