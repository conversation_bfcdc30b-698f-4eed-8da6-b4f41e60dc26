"use server"

import { cache } from "react"
import { redirect } from "next/navigation"
import { formEntries } from "@/utils/form-entries"
import { z } from "zod"

import { comparePasswords, createSession } from "@/lib/auth"
import prisma from "@/lib/prisma"

import { sendEmailVerification } from "../_lib/email-verification"

// دالة البحث عن المستخدم في قاعدة البيانات باستخدام cache
const findUserByEmail = cache(async (email: string) => {
  return await prisma.user.findUnique({
    where: { email },
    include: {
      bookOrders: { where: { status: "PAID" } },
      courseOrders: { where: { status: "PAID" } },
    },
  })
})

export async function loginAction(formData: FormData): Promise<{
  state: { message?: string; success: boolean }
  errors?: { email?: string[]; password?: string[] }
}> {
  const request = formEntries(formData)

  // ================================================================================== //
  // التحقق من البيانات باستخدام مكتبة زود
  // ================================================================================== //
  const schema = z.object({
    email: z.string().trim().email("الرجاء إدخال بريد إلكتروني صالح."),
    password: z
      .string()
      .trim()
      .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
  })

  const { success, data, error } = schema.safeParse(request)

  if (!success) {
    return {
      state: { success, message: "البيانات المدخلة غير صحيحة" },
      errors: error?.flatten().fieldErrors,
    }
  }

  // ================================================================================== //
  // التحقق من وجود البريد الإكتروني في قاعدة البيانات
  // ================================================================================== //
  const user = await findUserByEmail(data.email)

  if (!user) {
    return {
      state: { success: false, message: "البيانات التي ادخلتها غير صحيحة" },
      errors: { email: ["كلمة المرور او البريد الإكتروني غير صحيح"] },
    }
  }

  // ================================================================================== //
  // فك تشفير كلمة المرور والتحقق من صحتها
  // ================================================================================== //
  const password = await comparePasswords(data.password, user.password)
  if (!password) {
    return {
      state: {
        success: false,
        message: "كلمة المرور او البريد الإكتروني غير صحيح",
      },
      errors: { password: ["كلمة المرور او البريد الإكتروني غير صحيح"] },
    }
  }

  // ================================================================================== //
  // التحقق من تأكيد البريد الإلكتروني
  // ================================================================================== //
  if (!user.emailVerified) {
    // إذا كان رمز التحقق منتهي الصلاحية، نقوم بإنشاء رمز جديد وإرساله
    if (
      !user.verificationToken ||
      !user.verificationTokenExpiry ||
      user.verificationTokenExpiry < new Date()
    ) {
      // إنشاء رمز التحقق الجديد
      const { isSended, verificationToken, verificationTokenExpiry } =
        await sendEmailVerification({
          userEmail: user.email,
          userName: user.name,
        })

      if (!isSended) {
        return {
          state: {
            success: false,
            message:
              "حدث خطأ أثناء إرسال رابط التحقق الى بريدك الإلكتروني. يرجى المحاولة مرة أخرى.",
          },
        }
      }

      await prisma.user.update({
        where: { id: user.id },
        data: {
          verificationToken,
          verificationTokenExpiry,
        },
      })

      redirect("/auth/waiting-verify?email=" + data.email)
    }

    redirect("/auth/waiting-verify?email=" + data.email)
  }

  // انشاء مصفوفة لمعرفات كل الدورات تدريبية والكتب التي اشترك فيها المستخدم
  const userBooksIds = user.bookOrders.map((book) => book.bookId)
  const userCoursesIds = user.courseOrders.map((course) => course.courseId)
  const purchasesIds = [...userBooksIds, ...userCoursesIds]

  // ================================================================================== //
  // انشاء توكن للمستخدم
  // ================================================================================== //
  await createSession({
    id: user.id,
    purchasesIds,
    name: user.name,
    email: user.email,
  })

  return { state: { success: true, message: "تم تسجيل الدخول بنجاح" } }
}
