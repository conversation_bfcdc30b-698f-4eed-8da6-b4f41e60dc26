import { Metadata } from "next"
import { Suspense } from "react"
import { notFound } from "next/navigation"
import prisma from "@/lib/prisma"
import { unsubscribeFromNewsletter } from "../_lib/newsletter-actions"
import UnsubscribeForm from "./_components/unsubscribe-form"

export const metadata: Metadata = {
  title: "إلغاء الاشتراك في النشرة البريدية",
  description: "إلغاء الاشتراك في النشرة البريدية - أكاديمية د. ناهد باشطح",
  robots: {
    index: false,
    follow: false,
  },
}

interface UnsubscribePageProps {
  searchParams: Promise<{ email?: string }>
}

async function UnsubscribeContent({ email }: { email?: string }) {
  if (!email) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          إلغاء الاشتراك في النشرة البريدية
        </h1>
        <p className="text-gray-600 mb-6">
          يرجى إدخال بريدك الإلكتروني لإلغاء الاشتراك
        </p>
        <UnsubscribeForm />
      </div>
    )
  }

  // التحقق من وجود البريد في قاعدة البيانات
  const subscriber = await prisma.newsletter.findUnique({
    where: { email }
  })

  if (!subscriber) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          البريد الإلكتروني غير موجود
        </h1>
        <p className="text-gray-600 mb-6">
          هذا البريد الإلكتروني غير مشترك في النشرة البريدية
        </p>
        <UnsubscribeForm />
      </div>
    )
  }

  if (!subscriber.isActive) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          تم إلغاء الاشتراك مسبقاً
        </h1>
        <p className="text-gray-600 mb-6">
          هذا البريد الإلكتروني غير مشترك حالياً في النشرة البريدية
        </p>
        <p className="text-sm text-gray-500">
          تم إلغاء الاشتراك في: {new Date(subscriber.unsubscribedAt!).toLocaleDateString('ar-SA')}
        </p>
      </div>
    )
  }

  return (
    <div className="text-center">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">
        تأكيد إلغاء الاشتراك
      </h1>
      <p className="text-gray-600 mb-2">
        هل أنت متأكد من رغبتك في إلغاء الاشتراك في النشرة البريدية؟
      </p>
      <p className="text-sm text-gray-500 mb-6" dir="ltr">
        {email}
      </p>
      <UnsubscribeForm email={email} showConfirmation />
    </div>
  )
}

export default async function UnsubscribePage(props: UnsubscribePageProps) {
  const searchParams = await props.searchParams
  const email = searchParams.email

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm border p-8">
        <Suspense fallback={
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحميل...</p>
          </div>
        }>
          <UnsubscribeContent email={email} />
        </Suspense>
      </div>
      
      {/* رابط العودة للموقع */}
      <div className="text-center mt-8">
        <a 
          href="/" 
          className="text-primary hover:text-primary/80 text-sm underline"
        >
          العودة إلى الموقع الرئيسي
        </a>
      </div>
    </div>
  )
}
