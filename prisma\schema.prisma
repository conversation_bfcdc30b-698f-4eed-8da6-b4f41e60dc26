// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_URL_UNPOOLED")
}

model Course {
  id                String        @id @default(uuid())
  title             String
  price             Float         @default(0.0)
  posterUrl         String
  features          String[]
  description       String
  modulesCount      Int           @default(0)
  previewInHomePage Boolean       @default(false)
  createdAt         DateTime      @default(now())
  lectures          Lecture[]
  orders            CourseOrder[]
  seoDescription    String        @default("")
  seokeywords       String[]      @default([])

  @@index([createdAt(sort: Desc)])
}

model Lecture {
  id             String   @id @default(uuid())
  title          String
  posterUrl      String
  course         Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId       String
  video          String?
  audio          String?
  pdf            String?
  seoDescription String   @default("")
  seokeywords    String[] @default([])
  createdAt      DateTime @default(now())

  @@index([courseId, createdAt(sort: Asc)])
}

model Testimony {
  id        String @id @default(uuid())
  testimony String
}

model Article {
  id             String   @id @default(uuid())
  title          String
  article        String   @db.Text
  createdAt      DateTime @default(now())
  seoDescription String   @default("")
  seokeywords    String[] @default([])

  @@index([createdAt(sort: Desc)])
}

model Book {
  id             String      @id @default(uuid())
  title          String
  summary        String
  coverImage     String
  pagesCount     Int
  createdAt      DateTime    @default(now())
  price          Float       @default(0.0)
  bookUrl        String
  orders         BookOrder[]
  seoDescription String      @default("")
  seokeywords    String[]    @default([])

  @@index([createdAt(sort: Desc)])
}

model Interview {
  id             String   @id @default(uuid())
  title          String
  description    String
  videoUrl       String
  createdAt      DateTime @default(now())
  thumbnail      String
  seoDescription String   @default("")
  seokeywords    String[] @default([])

  @@index([createdAt(sort: Desc)])
}

model BlogPost {
  id             String   @id @default(uuid())
  title          String
  content        String   @db.Text
  createdAt      DateTime @default(now())
  image          String
  seoDescription String   @default("")
  seokeywords    String[] @default([])

  @@index([createdAt(sort: Asc)])
}

model Comment {
  id        String     @id @default(uuid())
  userId    String
  userName  String
  entityId  String
  comment   String
  entity    EntityType
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime   @default(now())

  @@index([entity, entityId, createdAt(sort: Desc)])
}

model User {
  id                      String        @id @default(uuid())
  name                    String
  email                   String        @unique
  password                String
  emailVerified           Boolean       @default(false)
  verificationTokenExpiry DateTime?
  verificationToken       String?
  Comments                Comment[]
  courseOrders            CourseOrder[]
  bookOrders              BookOrder[]
}

model CourseOrder {
  id        String      @id @default(uuid())
  userId    String?
  user      User?       @relation(fields: [userId], references: [id])
  courseId  String
  course    Course      @relation(fields: [courseId], references: [id], onDelete: Cascade)
  price     Float
  status    OrderStatus @default(PENDING)
  createdAt DateTime    @default(now())

  @@index([userId])
}

model BookOrder {
  id        String      @id @default(uuid())
  userId    String?
  user      User?       @relation(fields: [userId], references: [id])
  bookId    String
  book      Book        @relation(fields: [bookId], references: [id])
  price     Float
  status    OrderStatus @default(PENDING)
  createdAt DateTime    @default(now())

  @@index([userId])
}

model Consultation {
  id        String   @id @default(uuid())
  name      String
  read      Boolean  @default(false)
  email     String
  phone     String
  message   String
  createdAt DateTime @default(now())

  @@index([createdAt(sort: Desc)])
}

model Faq {
  id        String   @id @default(uuid())
  question  String
  answer    String
  createdAt DateTime @default(now())

  @@index([createdAt(sort: Desc)])
}

// مشرف لوحة ادارة المحتوى (لوحة التحكم)
model Admin {
  id               String      @id @default(uuid())
  name             String
  email            String      @unique
  password         String
  accessiblePages  String[]    @default([])
  avatarUrl        String?
  pushSubscription String?
  status           AdminStatus @default(activated)
  role             AdminRole   @default(admin)
}

model SiteSettings {
  id                       Int    @id @default(1)
  phone                    String @default("")
  email                    String @default("")
  facebookUrl              String @default("")
  twitterUrl               String @default("")
  instagramUrl             String @default("")
  youtubeUrl               String @default("")
  tiktokUrl                String @default("")
  myStoryVideoUrl          String @default("")
  myStoryVideoThumbnailUrl String @default("")
  subscriptionInstructions String @default("")
}

enum OrderStatus {
  PENDING // لم يتم الدفع بعد
  PAID // تم الدفع بنجاح
  FAILED // فشل الدفع
}

enum EntityType {
  lecture
  book
  interview
  article
  blogPost
}

enum AdminStatus {
  activated
  disabled
}

enum AdminRole {
  superadmin
  admin
}
