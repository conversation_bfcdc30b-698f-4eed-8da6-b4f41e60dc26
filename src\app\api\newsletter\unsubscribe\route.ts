import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import prisma from "@/lib/prisma"

// Schema للتحقق من صحة البيانات
const unsubscribeSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صحيح"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = unsubscribeSchema.parse(body)
    const { email } = validatedData

    // البحث عن المشترك
    const subscriber = await prisma.newsletter.findUnique({
      where: { email }
    })

    if (!subscriber) {
      return NextResponse.json(
        { 
          success: false, 
          message: "هذا البريد الإلكتروني غير مشترك في النشرة البريدية" 
        },
        { status: 404 }
      )
    }

    if (!subscriber.isActive) {
      return NextResponse.json(
        { 
          success: false, 
          message: "هذا البريد الإلكتروني غير مشترك حالياً في النشرة البريدية" 
        },
        { status: 400 }
      )
    }

    // إلغاء الاشتراك
    await prisma.newsletter.update({
      where: { email },
      data: { 
        isActive: false,
        unsubscribedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      message: "تم إلغاء اشتراكك في النشرة البريدية بنجاح. نأسف لرؤيتك تغادر!"
    })

  } catch (error) {
    console.error("Newsletter unsubscribe error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: error.errors[0]?.message || "البيانات المدخلة غير صحيحة" 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        message: "حدث خطأ أثناء إلغاء الاشتراك. يرجى المحاولة مرة أخرى." 
      },
      { status: 500 }
    )
  }
}

// GET endpoint لصفحة إلغاء الاشتراك
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const email = searchParams.get('email')

  if (!email) {
    return NextResponse.json(
      { success: false, message: "البريد الإلكتروني مطلوب" },
      { status: 400 }
    )
  }

  try {
    // التحقق من وجود المشترك
    const subscriber = await prisma.newsletter.findUnique({
      where: { email }
    })

    return NextResponse.json({
      success: true,
      exists: !!subscriber,
      isActive: subscriber?.isActive || false
    })

  } catch (error) {
    console.error("Newsletter check error:", error)
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء التحقق من الاشتراك" },
      { status: 500 }
    )
  }
}
