"use client"

import React, { useState, useTransition } from "react"
import { Send, Mail, Users, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

interface NewsletterSendFormProps {
  activeSubscribersCount: number
  onSend: (formData: FormData) => Promise<{
    success: boolean
    message: string
    details?: {
      totalSent: number
      totalFailed: number
      results: any[]
    }
  }>
}

export default function NewsletterSendForm({ 
  activeSubscribersCount, 
  onSend 
}: NewsletterSendFormProps) {
  const [subject, setSubject] = useState("")
  const [content, setContent] = useState("")
  const [result, setResult] = useState<{
    success: boolean
    message: string
    details?: any
  } | null>(null)
  const [isPending, startTransition] = useTransition()

  const handleSubmit = async (formData: FormData) => {
    if (!subject.trim() || !content.trim()) {
      setResult({
        success: false,
        message: "يرجى ملء جميع الحقول المطلوبة"
      })
      return
    }

    startTransition(async () => {
      try {
        const response = await onSend(formData)
        setResult(response)
        
        if (response.success) {
          // مسح النموذج عند النجاح
          setSubject("")
          setContent("")
        }
      } catch (error) {
        setResult({
          success: false,
          message: "حدث خطأ غير متوقع"
        })
      }
    })
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="space-y-6">
        {/* عنوان النموذج */}
        <div className="flex items-center gap-3">
          <Mail className="size-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">إنشاء نشرة بريدية جديدة</h2>
            <p className="text-sm text-gray-600">
              قم بكتابة محتوى النشرة البريدية التي ستُرسل للمشتركين
            </p>
          </div>
        </div>

        {/* معلومات المشتركين */}
        <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
          <Users className="size-5 text-gray-600" />
          <span className="text-sm text-gray-700">
            سيتم الإرسال إلى <strong>{activeSubscribersCount}</strong> مشترك نشط
          </span>
        </div>

        {/* النموذج */}
        <form action={handleSubmit} className="space-y-4">
          {/* موضوع النشرة */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
              موضوع النشرة البريدية *
            </label>
            <Input
              id="subject"
              name="subject"
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="أدخل موضوع النشرة البريدية..."
              required
              disabled={isPending}
              className="w-full"
            />
          </div>

          {/* محتوى النشرة */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
              محتوى النشرة البريدية *
            </label>
            <Textarea
              id="content"
              name="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="اكتب محتوى النشرة البريدية هنا... يمكنك استخدام HTML للتنسيق"
              required
              disabled={isPending}
              rows={12}
              className="w-full resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              يمكنك استخدام HTML للتنسيق (مثل: &lt;h2&gt;، &lt;p&gt;، &lt;strong&gt;، &lt;a&gt;)
            </p>
          </div>

          {/* تحذير */}
          <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="size-5 text-yellow-600 mt-0.5 shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">تنبيه مهم:</p>
              <p className="text-yellow-700 mt-1">
                تأكد من مراجعة المحتوى جيداً قبل الإرسال. لا يمكن التراجع عن إرسال النشرة البريدية.
              </p>
            </div>
          </div>

          {/* زر الإرسال */}
          <Button
            type="submit"
            disabled={isPending || !subject.trim() || !content.trim() || activeSubscribersCount === 0}
            className="w-full h-12 text-base"
            size="lg"
          >
            {isPending ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-current border-t-transparent" />
                جاري الإرسال...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Send className="size-5" />
                إرسال النشرة البريدية ({activeSubscribersCount} مشترك)
              </div>
            )}
          </Button>
        </form>

        {/* نتيجة الإرسال */}
        {result && (
          <div className={`p-4 rounded-lg border ${
            result.success 
              ? "bg-green-50 border-green-200" 
              : "bg-red-50 border-red-200"
          }`}>
            <div className="flex items-start gap-3">
              {result.success ? (
                <CheckCircle className="size-5 text-green-600 mt-0.5 shrink-0" />
              ) : (
                <XCircle className="size-5 text-red-600 mt-0.5 shrink-0" />
              )}
              <div>
                <p className={`font-medium ${
                  result.success ? "text-green-800" : "text-red-800"
                }`}>
                  {result.success ? "تم الإرسال بنجاح!" : "فشل في الإرسال"}
                </p>
                <p className={`text-sm mt-1 ${
                  result.success ? "text-green-700" : "text-red-700"
                }`}>
                  {result.message}
                </p>
                
                {result.success && result.details && (
                  <div className="mt-3 text-sm text-green-700">
                    <p>• تم الإرسال بنجاح: {result.details.totalSent} مشترك</p>
                    {result.details.totalFailed > 0 && (
                      <p>• فشل الإرسال: {result.details.totalFailed} مشترك</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
