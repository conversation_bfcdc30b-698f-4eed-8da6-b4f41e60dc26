import { revalidatePath } from "next/cache"
import prisma from "@/lib/prisma"
import NewsletterTable from "@/components/dashboard-workspace/newsletter-CRUD/NewsletterTable"

export default async function NewsletterPage() {
  const subscribers = await prisma.newsletter.findMany({
    orderBy: { subscribedAt: "desc" },
  })

  // إحصائيات المشتركين
  const stats = {
    total: subscribers.length,
    active: subscribers.filter(s => s.isActive).length,
    inactive: subscribers.filter(s => !s.isActive).length,
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900">إجمالي المشتركين</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{stats.total}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900">المشتركين النشطين</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{stats.active}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900">المشتركين غير النشطين</h3>
          <p className="text-3xl font-bold text-red-600 mt-2">{stats.inactive}</p>
        </div>
      </div>

      {/* جدول المشتركين */}
      <NewsletterTable
        data={subscribers}
        onDelete={async ({ data }) => {
          "use server"

          await prisma.newsletter.deleteMany({
            where: { id: { in: data.map((subscriber) => subscriber.id) } },
          })

          revalidatePath("/admin/newsletter")

          return { success: true, message: "تم حذف المشتركين بنجاح" }
        }}
        onToggleStatus={async (id: string, isActive: boolean) => {
          "use server"

          await prisma.newsletter.update({
            where: { id },
            data: { 
              isActive,
              unsubscribedAt: isActive ? null : new Date()
            },
          })

          revalidatePath("/admin/newsletter")

          return { success: true, message: `تم ${isActive ? 'تفعيل' : 'إلغاء'} الاشتراك بنجاح` }
        }}
      />
    </div>
  )
}
