import { ForwardRefExoticComponent, JS<PERSON>, RefAttributes, Suspense } from "react"
import Link from "next/link"
import { LucideProps, Mail, PhoneIncoming } from "lucide-react"

import { getSiteSettings } from "@/lib/querys"

import { Skeleton } from "../ui/skeleton"
import {
  FacebookIcon,
  InstagramIcon,
  Tiktok,
  TwitterIcon,
  YouTubeIcon,
} from "../ui/svg/icons"
import { LogoLargeFooter } from "../ui/svg/logos"
import { itemsNav } from "./nav"

type ContactsUsType = {
  Icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >
  contact: string
  url: string
}

const footerNav = [
  ...itemsNav,
  {
    label: "أسئلة شائعة",
    path: "/faq",
  },
]

type SocialMediaType = {
  Icons: ({ className }: { className?: string }) => JSX.Element
  url: string
}[]

function NavItems() {
  return footerNav.map(({ label, path }) => (
    <div key={path} className="relative">
      <ol className="text-background text-nowrap">
        <li className="text-background list-item list-inside list-disc text-nowrap">
          <Link
            className="text-background max-w-fit text-base font-normal text-nowrap hover:opacity-80"
            href={path}
          >
            {label}
          </Link>
        </li>
      </ol>
    </div>
  ))
}

async function SocialMedia() {
  const siteSettings = await getSiteSettings()

  const socialMediaItems: SocialMediaType = [
    {
      Icons: FacebookIcon,
      url: siteSettings.facebookUrl ?? "#",
    },
    {
      Icons: InstagramIcon,
      url: siteSettings.instagramUrl ?? "#",
    },
    {
      Icons: TwitterIcon,
      url: siteSettings.twitterUrl ?? "#",
    },
    {
      Icons: YouTubeIcon,
      url: siteSettings.youtubeUrl ?? "#",
    },
    {
      Icons: Tiktok,
      url: siteSettings.tiktokUrl ?? "#",
    },
  ]

  return socialMediaItems.map(({ Icons, url }, index) => (
    <a
      key={index}
      href={url}
      target="_blank"
      className="hover:bg-background/5 shrink-0 rounded-lg transition-all hover:scale-105 hover:opacity-90"
    >
      <span className="sr-only">حسابات التواصل الإجتماعي</span>
      {
        <Icons className="size-9 shrink-0 drop-shadow-[0_0_2px_#00000080] sm:size-11" />
      }
    </a>
  ))
}

async function ContactsUs() {
  const siteSettings = await getSiteSettings()

  const contactsUsItems: ContactsUsType[] = [
    {
      Icon: Mail,
      contact: siteSettings.email ?? "#",
      url: siteSettings.email ? `mailto:${siteSettings.email}` : "#",
    },
    {
      Icon: PhoneIncoming,
      contact: siteSettings.phone ?? "#",
      url: siteSettings.phone ? `tel:${siteSettings.phone}` : "#",
    },
  ]

  return contactsUsItems.map(({ contact, url, Icon }, index) => (
    <div key={index} className="text-background">
      <a
        dir="ltr"
        className="text-background flex max-w-fit items-center gap-x-4 text-lg font-normal hover:opacity-80"
        target="_blank"
        href={url}
      >
        {contact}
        <Icon className="size-5 shrink-0" />
      </a>
    </div>
  ))
}

export function Footer() {
  return (
    <footer className="bg-secondary overflow-x-clip rounded-t-xl pt-20">
      <div className="flex items-center gap-y-20 px-6 max-md:flex-col md:flex-wrap md:items-end md:justify-around md:gap-x-10">
        {/* االشعار */}
        <div className="flex flex-col items-center gap-y-4">
          <LogoLargeFooter className="max-w-64 sm:max-w-none" />
          <div className="flex items-center gap-x-6">
            <Suspense
              fallback={
                <>
                  <Skeleton className="size-9 shrink-0 sm:size-11" />
                  <Skeleton className="size-9 shrink-0 sm:size-11" />
                  <Skeleton className="size-9 shrink-0 sm:size-11" />
                  <Skeleton className="size-9 shrink-0 sm:size-11" />
                  <Skeleton className="size-9 shrink-0 sm:size-11" />
                </>
              }
            >
              <SocialMedia />
            </Suspense>
          </div>
        </div>

        {/* القائمة */}
        <div className="relative flex w-60 flex-col gap-y-3">
          <div className="absolute -top-5 right-0 -bottom-5 left-0 mr-[3px] rounded-xl border-2 drop-shadow-md"></div>
          <NavItems />
        </div>

        {/* البريد ورقم الهاتف */}
        <div className="w-60">
          <p className="text-background text-3xl font-bold">تواصـــل معنا</p>
          <div className="mt-7 flex flex-col gap-y-3">
            <ContactsUs />
          </div>
        </div>
      </div>

      {/* الحقوق */}
      <div className="mt-20 w-full">
        <div className="from-background/0 via-background/60 h-px w-full bg-linear-to-l" />
        <p className="text-background w-full py-3 text-center text-xs">
          <a
            href="http://smart-fingers.com/"
            target="_blank"
            className="underline"
          >
            شـركة الأنــامل الــذكية للبرمجيــات
          </a>
          . جميع الحقوق محفوظة © 2024
        </p>
      </div>
    </footer>
  )
}
