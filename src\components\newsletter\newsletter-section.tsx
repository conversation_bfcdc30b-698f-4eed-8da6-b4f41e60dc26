import React from "react"
import { <PERSON>, <PERSON>, Star } from "lucide-react"
import { NewsletterSubscription } from "./newsletter-subscription"
import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers"

export function NewsletterSection() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-50 to-purple-50 overflow-hidden">
      <DividerRedTop />
      
      {/* خلفية زخرفية */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 right-10 w-20 h-20 bg-primary rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-purple-500 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-blue-500 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* العنوان الرئيسي */}
          <div className="mb-8">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Mail className="size-16 text-primary" />
                <div className="absolute -top-2 -right-2 bg-yellow-400 rounded-full p-1">
                  <Bell className="size-4 text-white" />
                </div>
              </div>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              اشترك في النشرة البريدية
            </h2>
            
            <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
              احصل على أحدث المقالات والنصائح في العلاج الشعوري والتطوير الشخصي مباشرة في بريدك الإلكتروني
            </p>
          </div>

          {/* المميزات */}
          <div className="grid md:grid-cols-3 gap-6 mb-10">
            <div className="flex flex-col items-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm">
              <div className="bg-blue-100 p-3 rounded-full mb-4">
                <Star className="size-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">محتوى حصري</h3>
              <p className="text-sm text-gray-600 text-center">
                مقالات ونصائح حصرية لا تُنشر في أي مكان آخر
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm">
              <div className="bg-green-100 p-3 rounded-full mb-4">
                <Bell className="size-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">تحديثات فورية</h3>
              <p className="text-sm text-gray-600 text-center">
                كن أول من يعلم بالدورات والبرامج الجديدة
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm">
              <div className="bg-purple-100 p-3 rounded-full mb-4">
                <Mail className="size-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">بدون إزعاج</h3>
              <p className="text-sm text-gray-600 text-center">
                نرسل المحتوى المفيد فقط، ويمكنك إلغاء الاشتراك في أي وقت
              </p>
            </div>
          </div>

          {/* نموذج الاشتراك */}
          <div className="max-w-lg mx-auto">
            <NewsletterSubscription
              variant="inline"
              showTitle={false}
              className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/50"
            />
          </div>

          {/* إحصائية بسيطة */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              انضم إلى آلاف المشتركين الذين يحصلون على محتوى قيم أسبوعياً
            </p>
          </div>
        </div>
      </div>

      <DividerRedBottom />
    </section>
  )
}

// مكون مبسط للاستخدام في أماكن أخرى
export function SimpleNewsletterSection() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            اشترك في النشرة البريدية
          </h2>
          <p className="text-gray-600 mb-8">
            احصل على أحدث المقالات والنصائح مباشرة في بريدك الإلكتروني
          </p>
          <NewsletterSubscription
            variant="inline"
            showTitle={false}
            className="bg-white rounded-lg p-6 shadow-sm border"
          />
        </div>
      </div>
    </section>
  )
}
