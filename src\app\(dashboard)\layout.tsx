import { Metadata } from "next"

import "./globals-dashboard.css"

import localFont from "next/font/local"
// هذا التخطيط يلتف حول صفحات لوحة التحكم بالكامل بالإضافة الى صفحة تسجيل الدخول الخاصة بلوحة التحكم
// قم بإستيراد الحزم المشتركة هنا, على سبيل المثال (الخطوط, ملف سي اس اس)

// import { Tajawal } from "next/font/google"
import { siteName } from "@/configs/site"

// const fontTajawal = Tajawal({
//   weight: ["200", "300", "400", "500", "700", "800", "900"],
//   subsets: ["arabic"],
//   display: "swap",
// })
const fontTajawal = localFont({
  src: [
    {
      path: "./../fonts/Tajawal-Regular.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-Medium.ttf",
      weight: "500",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-Bold.ttf",
      weight: "700",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-ExtraBold.ttf",
      weight: "800",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-Black.ttf",
      weight: "900",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-Light.ttf",
      weight: "300",
      style: "normal",
    },
    {
      path: "./../fonts/Tajawal-ExtraLight.ttf",
      weight: "200",
      style: "normal",
    },
  ],
  display: "swap",
})

export const metadata: Metadata = {
  title: `لوحة التحكم | ${siteName}`,
  robots: {
    index: false,
    follow: false,
    googleBot: { index: false, follow: false },
  },
}

type Props = { children: React.ReactNode }
export default function DashboardMainLayout({ children }: Props) {
  return (
    <main
      className={`fontl h-full w-full text-gray-800 dark:text-gray-200 ${fontTajawal.className}`}
    >
      {children}
    </main>
  )
}
