"use server"

import Fuse from "fuse.js"

import { getPageTitlesAndIds } from "@/lib/querys"

export type SearchablePagesIds =
  | "blogPosts"
  | "courses"
  | "articles"
  | "interviews"
  | "books"

type Item = { id: string; title: string }

export type GetSearchQueryResult = {
  id: SearchablePagesIds
  data: Item[]
}[]

const defaultData: GetSearchQueryResult = [
  { id: "courses", data: [] },
  { id: "articles", data: [] },
  { id: "blogPosts", data: [] },
  { id: "interviews", data: [] },
  { id: "books", data: [] },
]

export const getSearchQuery = async (
  query: string
): Promise<GetSearchQueryResult> => {
  if (query === "") return defaultData

  const searchData = (await getPageTitlesAndIds())
    .map((item) => {
      const fuse = new Fuse(item.data, {
        keys: ["title"],
        includeMatches: true,
        threshold: 0.3,
        ignoreDiacritics: true,
      })
      const data = fuse
        .search(query)
        .map((e) => e.item)
        .slice(0, 20)

      return { ...item, data }
    })
    .filter((value) => value.data.length)

  return searchData as GetSearchQueryResult
}
