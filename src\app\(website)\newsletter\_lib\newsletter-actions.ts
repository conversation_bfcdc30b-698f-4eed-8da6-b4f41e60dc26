"use server"

import { z } from "zod"
import { revalidateTag } from "next/cache"
import prisma from "@/lib/prisma"
import { sendNewsletterWelcomeEmail, sendNewsletterGoodbyeEmail } from "./newsletter-emails"

// Schema للتحقق من صحة البيانات
const emailSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صحيح"),
})

/**
 * Server Action للاشتراك في النشرة البريدية
 */
export async function subscribeToNewsletter(formData: FormData) {
  try {
    const email = formData.get("email") as string
    
    // التحقق من صحة البيانات
    const validatedData = emailSchema.parse({ email })
    const validEmail = validatedData.email

    // التحقق من عدم وجود البريد مسبقاً
    const existingSubscriber = await prisma.newsletter.findUnique({
      where: { email: validEmail }
    })

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        return {
          success: false,
          message: "هذا البريد الإلكتروني مشترك بالفعل في النشرة البريدية"
        }
      } else {
        // إعادة تفعيل الاشتراك إذا كان ملغياً
        await prisma.newsletter.update({
          where: { email: validEmail },
          data: { 
            isActive: true,
            subscribedAt: new Date(),
            unsubscribedAt: null
          }
        })

        // إرسال بريد الترحيب
        await sendNewsletterWelcomeEmail(validEmail)
        
        // إعادة تحديث البيانات
        revalidateTag("newsletter-subscribers")
        
        return {
          success: true,
          message: "تم تجديد اشتراكك في النشرة البريدية بنجاح!"
        }
      }
    }

    // إنشاء اشتراك جديد
    await prisma.newsletter.create({
      data: { email: validEmail }
    })

    // إرسال بريد الترحيب
    await sendNewsletterWelcomeEmail(validEmail)

    // إعادة تحديث البيانات
    revalidateTag("newsletter-subscribers")

    return {
      success: true,
      message: "تم الاشتراك في النشرة البريدية بنجاح! سنرسل لك أحدث المقالات والأخبار."
    }

  } catch (error) {
    console.error("Newsletter subscription error:", error)
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: error.errors[0]?.message || "البيانات المدخلة غير صحيحة"
      }
    }

    return {
      success: false,
      message: "حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى."
    }
  }
}

/**
 * Server Action لإلغاء الاشتراك في النشرة البريدية
 */
export async function unsubscribeFromNewsletter(formData: FormData) {
  try {
    const email = formData.get("email") as string
    
    // التحقق من صحة البيانات
    const validatedData = emailSchema.parse({ email })
    const validEmail = validatedData.email

    // البحث عن المشترك
    const subscriber = await prisma.newsletter.findUnique({
      where: { email: validEmail }
    })

    if (!subscriber) {
      return {
        success: false,
        message: "هذا البريد الإلكتروني غير مشترك في النشرة البريدية"
      }
    }

    if (!subscriber.isActive) {
      return {
        success: false,
        message: "هذا البريد الإلكتروني غير مشترك حالياً في النشرة البريدية"
      }
    }

    // إلغاء الاشتراك
    await prisma.newsletter.update({
      where: { email: validEmail },
      data: { 
        isActive: false,
        unsubscribedAt: new Date()
      }
    })

    // إرسال بريد تأكيد إلغاء الاشتراك
    await sendNewsletterGoodbyeEmail(validEmail)

    // إعادة تحديث البيانات
    revalidateTag("newsletter-subscribers")

    return {
      success: true,
      message: "تم إلغاء اشتراكك في النشرة البريدية بنجاح. نأسف لرؤيتك تغادر!"
    }

  } catch (error) {
    console.error("Newsletter unsubscribe error:", error)
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: error.errors[0]?.message || "البيانات المدخلة غير صحيحة"
      }
    }

    return {
      success: false,
      message: "حدث خطأ أثناء إلغاء الاشتراك. يرجى المحاولة مرة أخرى."
    }
  }
}

/**
 * Server Action للحصول على إحصائيات المشتركين
 */
export async function getNewsletterStats() {
  try {
    const [totalSubscribers, activeSubscribers, inactiveSubscribers] = await Promise.all([
      prisma.newsletter.count(),
      prisma.newsletter.count({ where: { isActive: true } }),
      prisma.newsletter.count({ where: { isActive: false } })
    ])

    return {
      success: true,
      stats: {
        total: totalSubscribers,
        active: activeSubscribers,
        inactive: inactiveSubscribers
      }
    }
  } catch (error) {
    console.error("Newsletter stats error:", error)
    return {
      success: false,
      message: "حدث خطأ أثناء جلب الإحصائيات"
    }
  }
}

/**
 * Server Action للحصول على قائمة المشتركين (للوحة التحكم)
 */
export async function getNewsletterSubscribers(page: number = 1, limit: number = 50) {
  try {
    const skip = (page - 1) * limit

    const [subscribers, total] = await Promise.all([
      prisma.newsletter.findMany({
        skip,
        take: limit,
        orderBy: { subscribedAt: 'desc' },
        select: {
          id: true,
          email: true,
          isActive: true,
          subscribedAt: true,
          unsubscribedAt: true
        }
      }),
      prisma.newsletter.count()
    ])

    return {
      success: true,
      data: {
        subscribers,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    }
  } catch (error) {
    console.error("Newsletter subscribers error:", error)
    return {
      success: false,
      message: "حدث خطأ أثناء جلب قائمة المشتركين"
    }
  }
}
